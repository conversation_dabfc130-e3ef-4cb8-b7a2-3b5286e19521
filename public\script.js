// Global variables
let isLoggedIn = false;
let accessToken = null;
let wardList = [];
let selectedWardIndex = -1;

// DOM elements
const loginBtn = document.getElementById('loginBtn');
const loginSpinner = document.getElementById('loginSpinner');
const loginResult = document.getElementById('loginResult');
const connectionStatus = document.getElementById('connectionStatus');
const connectionText = document.getElementById('connectionText');
const supplementCard = document.getElementById('supplementCard');
const supplementForm = document.getElementById('supplementForm');
const supplementSpinner = document.getElementById('supplementSpinner');
const supplementResult = document.getElementById('supplementResult');
const wardSearch = document.getElementById('wardSearch');
const wardDropdown = document.getElementById('wardDropdown');
const xaIdInput = document.getElementById('xaId');

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    updateConnectionStatus();
    setDefaultDateTime();
    loadWardList();
    setupWardSearch();
});

// Set default date time to current time
function setDefaultDateTime() {
    const now = new Date();
    const day = String(now.getDate()).padStart(2, '0');
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const year = now.getFullYear();
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');

    const formattedDateTime = `${day}/${month}/${year} ${hour}:${minute}:${second}`;
    document.getElementById('NgayHenTraMoi').value = formattedDateTime;
}

// Load ward list from API
async function loadWardList() {
    try {
        const response = await fetch('/api/wards');
        const data = await response.json();

        if (data.success) {
            wardList = data.data;

            // Set default selection to "Xã Hồ Thị Kỷ" (32092)
            const defaultWard = wardList.find(ward => ward.code === '32092');
            if (defaultWard) {
                selectWard(defaultWard);
            }
        }
    } catch (error) {
        console.error('Error loading ward list:', error);
        showAlert('loginResult', 'Không thể tải danh sách xã/phường', 'warning');
    }
}

// Update connection status UI
function updateConnectionStatus() {
    if (isLoggedIn) {
        connectionStatus.className = 'status-indicator status-connected';
        connectionText.textContent = 'Đã kết nối';
        loginBtn.innerHTML = '<i class="fas fa-check me-2"></i>Đã đăng nhập';
        loginBtn.disabled = true;
        supplementCard.style.display = 'block';
    } else {
        connectionStatus.className = 'status-indicator status-disconnected';
        connectionText.textContent = 'Chưa kết nối';
        loginBtn.innerHTML = '<i class="fas fa-key me-2"></i>Đăng nhập';
        loginBtn.disabled = false;
        supplementCard.style.display = 'none';
    }
}

// Show loading state
function showLoading(spinner, button) {
    spinner.style.display = 'inline-block';
    button.disabled = true;
}

// Hide loading state
function hideLoading(spinner, button) {
    spinner.style.display = 'none';
    button.disabled = false;
}

// Show alert message
function showAlert(containerId, message, type = 'info') {
    const container = document.getElementById(containerId);
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const icon = type === 'success' ? 'fas fa-check-circle' : 
                 type === 'error' ? 'fas fa-exclamation-circle' : 
                 type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';
    
    container.innerHTML = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
}

// Login function
async function login() {
    showLoading(loginSpinner, loginBtn);
    
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            isLoggedIn = true;
            accessToken = data.token;
            updateConnectionStatus();
            showAlert('loginResult', `${data.message}. Token sẽ hết hạn sau ${data.expires_in} giây.`, 'success');
        } else {
            showAlert('loginResult', `Lỗi: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showAlert('loginResult', `Lỗi kết nối: ${error.message}`, 'error');
    } finally {
        hideLoading(loginSpinner, loginBtn);
    }
}

// Handle supplement form submission
supplementForm.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!isLoggedIn) {
        showAlert('supplementResult', 'Vui lòng đăng nhập trước khi thực hiện thao tác này.', 'warning');
        return;
    }
    
    const formData = new FormData(supplementForm);
    const dateInput = document.getElementById('NgayHenTraMoi').value;

    let parsedDate;
    try {
        parsedDate = parseDateTime(dateInput);
    } catch (error) {
        showAlert('supplementResult', error.message, 'error');
        return;
    }

    const data = {
        xaId: xaIdInput.value,
        NgayHenTraMoi: parsedDate,
        SoBienNhan: document.getElementById('SoBienNhan').value
    };

    // Validate required fields
    if (!data.xaId || !data.NgayHenTraMoi || !data.SoBienNhan) {
        showAlert('supplementResult', 'Vui lòng điền đầy đủ tất cả các trường bắt buộc.', 'warning');
        return;
    }
    
    const submitBtn = supplementForm.querySelector('button[type="submit"]');
    showLoading(supplementSpinner, submitBtn);
    
    try {
        const response = await fetch('/api/supplement-document', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('supplementResult', `${result.message}`, 'success');
            
            // Show response data if available
            if (result.data) {
                const dataContainer = document.createElement('div');
                dataContainer.className = 'mt-3 p-3 bg-light rounded';
                dataContainer.innerHTML = `
                    <h6><i class="fas fa-database me-2"></i>Dữ liệu phản hồi:</h6>
                    <pre class="mb-0">${JSON.stringify(result.data, null, 2)}</pre>
                `;
                document.getElementById('supplementResult').appendChild(dataContainer);
            }
        } else {
            showAlert('supplementResult', `Lỗi: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('Supplement error:', error);
        showAlert('supplementResult', `Lỗi kết nối: ${error.message}`, 'error');
    } finally {
        hideLoading(supplementSpinner, submitBtn);
    }
});

// Setup ward search functionality
function setupWardSearch() {
    // Show dropdown when input is focused
    wardSearch.addEventListener('focus', function() {
        showWardDropdown();
    });

    // Filter wards as user types
    wardSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        filterWards(searchTerm);
        showWardDropdown();
    });

    // Handle keyboard navigation
    wardSearch.addEventListener('keydown', function(e) {
        const items = wardDropdown.querySelectorAll('.dropdown-item');

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            selectedWardIndex = Math.min(selectedWardIndex + 1, items.length - 1);
            updateSelection(items);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            selectedWardIndex = Math.max(selectedWardIndex - 1, -1);
            updateSelection(items);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (selectedWardIndex >= 0 && items[selectedWardIndex]) {
                items[selectedWardIndex].click();
            }
        } else if (e.key === 'Escape') {
            hideWardDropdown();
        }
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!wardSearch.contains(e.target) && !wardDropdown.contains(e.target)) {
            hideWardDropdown();
        }
    });
}

// Filter wards based on search term
function filterWards(searchTerm) {
    const filteredWards = wardList.filter(ward =>
        ward.name.toLowerCase().includes(searchTerm) ||
        ward.code.includes(searchTerm)
    );

    renderWardDropdown(filteredWards);
    selectedWardIndex = -1;
}

// Render ward dropdown
function renderWardDropdown(wards) {
    wardDropdown.innerHTML = '';

    if (wards.length === 0) {
        wardDropdown.innerHTML = '<div class="dropdown-item text-muted">Không tìm thấy kết quả</div>';
        return;
    }

    wards.forEach(ward => {
        const item = document.createElement('div');
        item.className = 'dropdown-item';
        item.innerHTML = `
            <span class="ward-code">${ward.code}</span> -
            <span class="ward-name">${ward.name}</span>
        `;

        item.addEventListener('click', function() {
            selectWard(ward);
            hideWardDropdown();
        });

        wardDropdown.appendChild(item);
    });
}

// Select a ward
function selectWard(ward) {
    wardSearch.value = `${ward.code} - ${ward.name}`;
    xaIdInput.value = ward.code;
}

// Show ward dropdown
function showWardDropdown() {
    if (wardList.length === 0) return;

    if (wardDropdown.children.length === 0) {
        renderWardDropdown(wardList);
    }

    wardDropdown.classList.add('show');
}

// Hide ward dropdown
function hideWardDropdown() {
    wardDropdown.classList.remove('show');
    selectedWardIndex = -1;
}

// Update selection highlighting
function updateSelection(items) {
    items.forEach((item, index) => {
        if (index === selectedWardIndex) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });
}

// Auto-format receipt number
document.getElementById('SoBienNhan').addEventListener('input', function(e) {
    // Optional: Add formatting logic for receipt number if needed
});

// Parse date time from various formats
function parseDateTime(dateInput) {
    if (!dateInput) return null;

    // If already in ISO format, return as is
    if (dateInput.includes('T') && dateInput.includes('Z')) {
        return dateInput;
    }

    // If already in ISO format without Z
    if (dateInput.includes('T') && !dateInput.includes('Z')) {
        return dateInput + (dateInput.includes('.') ? '' : '.000') + 'Z';
    }

    // Try to parse various formats
    let date;

    // Format: dd/mm/yyyy hh:mm:ss
    if (dateInput.match(/^\d{1,2}\/\d{1,2}\/\d{4}\s+\d{1,2}:\d{2}:\d{2}$/)) {
        const [datePart, timePart] = dateInput.split(' ');
        const [day, month, year] = datePart.split('/');
        const [hour, minute, second] = timePart.split(':');
        date = new Date(year, month - 1, day, hour, minute, second);
    }
    // Format: dd/mm/yyyy hh:mm
    else if (dateInput.match(/^\d{1,2}\/\d{1,2}\/\d{4}\s+\d{1,2}:\d{2}$/)) {
        const [datePart, timePart] = dateInput.split(' ');
        const [day, month, year] = datePart.split('/');
        const [hour, minute] = timePart.split(':');
        date = new Date(year, month - 1, day, hour, minute);
    }
    // Format: dd/mm/yyyy
    else if (dateInput.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)) {
        const [day, month, year] = dateInput.split('/');
        date = new Date(year, month - 1, day, 10, 0); // Default to 10:00 AM
    }
    // Format: yyyy-mm-dd hh:mm
    else if (dateInput.match(/^\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2}$/)) {
        date = new Date(dateInput);
    }
    // Format: yyyy-mm-dd
    else if (dateInput.match(/^\d{4}-\d{1,2}-\d{1,2}$/)) {
        date = new Date(dateInput + ' 10:00'); // Default to 10:00 AM
    }
    // Try to parse as is
    else {
        date = new Date(dateInput);
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
        throw new Error('Định dạng ngày không hợp lệ. Vui lòng sử dụng: dd/mm/yyyy hh:mm hoặc yyyy-mm-dd hh:mm hoặc ISO format');
    }

    return date.toISOString();
}

// Add some utility functions
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('vi-VN');
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Could add a toast notification here
        console.log('Copied to clipboard');
    });
}
