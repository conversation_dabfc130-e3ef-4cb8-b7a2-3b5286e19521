// Global variables
let isLoggedIn = false;
let accessToken = null;

// DOM elements
const loginBtn = document.getElementById('loginBtn');
const loginSpinner = document.getElementById('loginSpinner');
const loginResult = document.getElementById('loginResult');
const connectionStatus = document.getElementById('connectionStatus');
const connectionText = document.getElementById('connectionText');
const supplementCard = document.getElementById('supplementCard');
const supplementForm = document.getElementById('supplementForm');
const supplementSpinner = document.getElementById('supplementSpinner');
const supplementResult = document.getElementById('supplementResult');

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    updateConnectionStatus();
    setDefaultDateTime();
});

// Set default date time to current time
function setDefaultDateTime() {
    const now = new Date();
    const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000);
    document.getElementById('NgayHenTraMoi').value = localDateTime.toISOString().slice(0, 16);
}

// Update connection status UI
function updateConnectionStatus() {
    if (isLoggedIn) {
        connectionStatus.className = 'status-indicator status-connected';
        connectionText.textContent = 'Đã kết nối';
        loginBtn.innerHTML = '<i class="fas fa-check me-2"></i>Đã đăng nhập';
        loginBtn.disabled = true;
        supplementCard.style.display = 'block';
    } else {
        connectionStatus.className = 'status-indicator status-disconnected';
        connectionText.textContent = 'Chưa kết nối';
        loginBtn.innerHTML = '<i class="fas fa-key me-2"></i>Đăng nhập';
        loginBtn.disabled = false;
        supplementCard.style.display = 'none';
    }
}

// Show loading state
function showLoading(spinner, button) {
    spinner.style.display = 'inline-block';
    button.disabled = true;
}

// Hide loading state
function hideLoading(spinner, button) {
    spinner.style.display = 'none';
    button.disabled = false;
}

// Show alert message
function showAlert(containerId, message, type = 'info') {
    const container = document.getElementById(containerId);
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const icon = type === 'success' ? 'fas fa-check-circle' : 
                 type === 'error' ? 'fas fa-exclamation-circle' : 
                 type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';
    
    container.innerHTML = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
}

// Login function
async function login() {
    showLoading(loginSpinner, loginBtn);
    
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            isLoggedIn = true;
            accessToken = data.token;
            updateConnectionStatus();
            showAlert('loginResult', `${data.message}. Token sẽ hết hạn sau ${data.expires_in} giây.`, 'success');
        } else {
            showAlert('loginResult', `Lỗi: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showAlert('loginResult', `Lỗi kết nối: ${error.message}`, 'error');
    } finally {
        hideLoading(loginSpinner, loginBtn);
    }
}

// Handle supplement form submission
supplementForm.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!isLoggedIn) {
        showAlert('supplementResult', 'Vui lòng đăng nhập trước khi thực hiện thao tác này.', 'warning');
        return;
    }
    
    const formData = new FormData(supplementForm);
    const data = {
        xaId: document.getElementById('xaId').value,
        NgayHenTraMoi: new Date(document.getElementById('NgayHenTraMoi').value).toISOString(),
        SoBienNhan: document.getElementById('SoBienNhan').value
    };
    
    // Validate required fields
    if (!data.xaId || !data.NgayHenTraMoi || !data.SoBienNhan) {
        showAlert('supplementResult', 'Vui lòng điền đầy đủ tất cả các trường bắt buộc.', 'warning');
        return;
    }
    
    const submitBtn = supplementForm.querySelector('button[type="submit"]');
    showLoading(supplementSpinner, submitBtn);
    
    try {
        const response = await fetch('/api/supplement-document', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('supplementResult', `${result.message}`, 'success');
            
            // Show response data if available
            if (result.data) {
                const dataContainer = document.createElement('div');
                dataContainer.className = 'mt-3 p-3 bg-light rounded';
                dataContainer.innerHTML = `
                    <h6><i class="fas fa-database me-2"></i>Dữ liệu phản hồi:</h6>
                    <pre class="mb-0">${JSON.stringify(result.data, null, 2)}</pre>
                `;
                document.getElementById('supplementResult').appendChild(dataContainer);
            }
        } else {
            showAlert('supplementResult', `Lỗi: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('Supplement error:', error);
        showAlert('supplementResult', `Lỗi kết nối: ${error.message}`, 'error');
    } finally {
        hideLoading(supplementSpinner, submitBtn);
    }
});

// Auto-format receipt number
document.getElementById('SoBienNhan').addEventListener('input', function(e) {
    // Optional: Add formatting logic for receipt number if needed
});

// Add some utility functions
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('vi-VN');
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Could add a toast notification here
        console.log('Copied to clipboard');
    });
}
