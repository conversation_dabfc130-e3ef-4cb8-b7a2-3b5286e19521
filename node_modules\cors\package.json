{"_from": "cors@2.8.5", "_id": "cors@2.8.5", "_inBundle": false, "_integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==", "_location": "/cors", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cors@2.8.5", "name": "cors", "escapedName": "cors", "rawSpec": "2.8.5", "saveSpec": null, "fetchSpec": "2.8.5"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "_shasum": "eac11da51592dd86b9f06f6e7ac293b3df875d29", "_spec": "cors@2.8.5", "_where": "D:\\tool iLis", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/troygoode/"}, "bugs": {"url": "https://github.com/expressjs/cors/issues"}, "bundleDependencies": false, "dependencies": {"object-assign": "^4", "vary": "^1"}, "deprecated": false, "description": "Node.js CORS middleware", "devDependencies": {"after": "0.8.2", "eslint": "2.13.1", "express": "4.16.3", "mocha": "5.2.0", "nyc": "13.1.0", "supertest": "3.3.0"}, "engines": {"node": ">= 0.10"}, "files": ["lib/index.js", "CONTRIBUTING.md", "HISTORY.md", "LICENSE", "README.md"], "homepage": "https://github.com/expressjs/cors#readme", "keywords": ["cors", "express", "connect", "middleware"], "license": "MIT", "main": "./lib/index.js", "name": "cors", "repository": {"type": "git", "url": "git+https://github.com/expressjs/cors.git"}, "scripts": {"lint": "eslint lib test", "test": "npm run lint && nyc --reporter=html --reporter=text mocha --require test/support/env"}, "version": "2.8.5"}