const express = require('express');
const cors = require('cors');
const axios = require('axios');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// API credentials
const API_CONFIG = {
    SSO_URL: 'https://ilis-sso.vnpt.vn/connect/token',
    GATEWAY_URL: 'https://ilis-gateway.vnpt.vn/lpm/api/DVCIGateV2/BoSungHoSo',
    CLIENT_CREDENTIALS: {
        username: 'admin.camau2',
        password: 'D2D@Kw2Kh2',
        grant_type: 'password',
        client_id: 'vilis-mobile-client',
        client_secret: 'n)3b^Q7g]Jd6T&$^'
    }
};

// Store token globally (in production, use proper session management)
let accessToken = null;

// Danh sách xã/phường
const WARD_LIST = [
    { code: '32002', name: '<PERSON><PERSON>ờ<PERSON>' },
    { code: '32014', name: '<PERSON><PERSON>ờ<PERSON>âm' },
    { code: '32025', name: 'Phường Tân Thành' },
    { code: '32041', name: 'Phường Hòa Thành' },
    { code: '32167', name: 'Xã Tân Thuận' },
    { code: '32188', name: 'Xã Tân Tiến' },
    { code: '32155', name: 'Xã Tạ An Khương' },
    { code: '32161', name: 'Xã Trần Phán' },
    { code: '32185', name: 'Xã Thanh Tùng' },
    { code: '32152', name: 'Xã Đầm Dơi' },
    { code: '32182', name: 'Xã Quách Phẩm' },
    { code: '32047', name: 'Xã U Minh' },
    { code: '32044', name: 'Xã Nguyễn Phích' },
    { code: '32062', name: 'Xã Khánh Lâm' },
    { code: '32059', name: 'Xã Khánh An' },
    { code: '32244', name: 'Xã Phan Ngọc Hiển' },
    { code: '32248', name: 'Xã Đất Mũi' },
    { code: '32236', name: 'Xã Tân Ân' },
    { code: '32110', name: 'Xã Khánh Bình' },
    { code: '32104', name: 'Xã Đá Bạc' },
    { code: '32119', name: 'Xã Khánh Hưng' },
    { code: '32098', name: 'Xã Sông Đốc' },
    { code: '32095', name: 'Xã Trần Văn Thời' },
    { code: '32065', name: 'Xã Thới Bình' },
    { code: '32071', name: 'Xã Trí Phải' },
    { code: '32083', name: 'Xã Tân Lộc' },
    { code: '32092', name: 'Xã Hồ Thị Kỷ' },
    { code: '32069', name: 'Xã Biển Bạch' },
    { code: '32201', name: 'Xã Đất Mới' },
    { code: '32191', name: 'Xã Năm Căn' },
    { code: '32206', name: 'Xã Tam Giang' },
    { code: '32212', name: 'Xã Cái Đôi Vàm' },
    { code: '32227', name: 'Xã Nguyễn Việt Khái' },
    { code: '32218', name: 'Xã Phú Tân' },
    { code: '32214', name: 'Xã Phú Mỹ' },
    { code: '32134', name: 'Xã Lương Thế Trân' },
    { code: '32137', name: 'Xã Tân Hưng' },
    { code: '32140', name: 'Xã Hưng Mỹ' },
    { code: '32128', name: 'Xã Cái Nước' },
    { code: '31825', name: 'Phường Bạc Liêu' },
    { code: '31834', name: 'Phường Vĩnh Trạch' },
    { code: '31840', name: 'Phường Hiệp Thành' },
    { code: '31942', name: 'Phường Giá Rai' },
    { code: '31951', name: 'Phường Láng Tròn' },
    { code: '31957', name: 'Xã Phong Thạnh' },
    { code: '31843', name: 'Xã Hồng Dân' },
    { code: '31858', name: 'Xã Vĩnh Lộc' },
    { code: '31864', name: 'Xã Ninh Thạnh Lợi' },
    { code: '31849', name: 'Xã Ninh Quới' },
    { code: '31972', name: 'Xã Gành Hào' },
    { code: '31993', name: 'Xã Định Thành' },
    { code: '31988', name: 'Xã An Trạch' },
    { code: '31985', name: 'Xã Long Điền' },
    { code: '31975', name: 'Xã Đông Hải' },
    { code: '31891', name: 'Xã Hòa Bình' },
    { code: '31918', name: 'Xã Vĩnh Mỹ' },
    { code: '31927', name: 'Xã Vĩnh Hậu' },
    { code: '31867', name: 'Xã Phước Long' },
    { code: '31876', name: 'Xã Vĩnh Phước' },
    { code: '31885', name: 'Xã Phong Hiệp' },
    { code: '31882', name: 'Xã Vĩnh Thanh' },
    { code: '31900', name: 'Xã Vĩnh Lợi' },
    { code: '31906', name: 'Xã Hưng Hội' },
    { code: '31894', name: 'Xã Châu Thới' }
];

// Route to get access token
app.post('/api/login', async (req, res) => {
    try {
        console.log('Attempting to get access token...');

        const response = await axios.post(API_CONFIG.SSO_URL, API_CONFIG.CLIENT_CREDENTIALS, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });

        accessToken = response.data.access_token;

        res.json({
            success: true,
            message: 'Đăng nhập thành công',
            token: accessToken,
            expires_in: response.data.expires_in
        });

        console.log('Login successful, token obtained');
    } catch (error) {
        console.error('Login error:', error.response?.data || error.message);
        res.status(500).json({
            success: false,
            message: 'Đăng nhập thất bại',
            error: error.response?.data || error.message
        });
    }
});

// Route to supplement document
app.post('/api/supplement-document', async (req, res) => {
    try {
        if (!accessToken) {
            return res.status(401).json({
                success: false,
                message: 'Vui lòng đăng nhập trước'
            });
        }

        const { xaId, NgayHenTraMoi, SoBienNhan } = req.body;

        if (!xaId || !NgayHenTraMoi || !SoBienNhan) {
            return res.status(400).json({
                success: false,
                message: 'Thiếu thông tin bắt buộc'
            });
        }

        console.log('Sending supplement request with data:', { xaId, NgayHenTraMoi, SoBienNhan });

        const response = await axios.post(API_CONFIG.GATEWAY_URL, {
            xaId,
            NgayHenTraMoi,
            SoBienNhan
        }, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        res.json({
            success: true,
            message: 'Bổ sung hồ sơ thành công',
            data: response.data
        });

        console.log('Supplement document successful');
    } catch (error) {
        console.error('Supplement document error:', error.response?.data || error.message);
        res.status(500).json({
            success: false,
            message: 'Bổ sung hồ sơ thất bại',
            error: error.response?.data || error.message
        });
    }
});

// Route to get ward list
app.get('/api/wards', (req, res) => {
    res.json({
        success: true,
        data: WARD_LIST
    });
});

// Serve main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start server
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});