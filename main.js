const express = require('express');
const cors = require('cors');
const axios = require('axios');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// API credentials
const API_CONFIG = {
    SSO_URL: 'https://ilis-sso.vnpt.vn/connect/token',
    GATEWAY_URL: 'https://ilis-gateway.vnpt.vn/lpm/api/DVCIGateV2/BoSungHoSo',
    CLIENT_CREDENTIALS: {
        username: 'admin.camau2',
        password: 'D2D@Kw2Kh2',
        grant_type: 'password',
        client_id: 'vilis-mobile-client',
        client_secret: 'n)3b^Q7g]Jd6T&$^'
    }
};

// Store token globally (in production, use proper session management)
let accessToken = null;

// Route to get access token
app.post('/api/login', async (req, res) => {
    try {
        console.log('Attempting to get access token...');

        const response = await axios.post(API_CONFIG.SSO_URL, API_CONFIG.CLIENT_CREDENTIALS, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });

        accessToken = response.data.access_token;

        res.json({
            success: true,
            message: 'Đăng nhập thành công',
            token: accessToken,
            expires_in: response.data.expires_in
        });

        console.log('Login successful, token obtained');
    } catch (error) {
        console.error('Login error:', error.response?.data || error.message);
        res.status(500).json({
            success: false,
            message: 'Đăng nhập thất bại',
            error: error.response?.data || error.message
        });
    }
});

// Route to supplement document
app.post('/api/supplement-document', async (req, res) => {
    try {
        if (!accessToken) {
            return res.status(401).json({
                success: false,
                message: 'Vui lòng đăng nhập trước'
            });
        }

        const { xaId, NgayHenTraMoi, SoBienNhan } = req.body;

        if (!xaId || !NgayHenTraMoi || !SoBienNhan) {
            return res.status(400).json({
                success: false,
                message: 'Thiếu thông tin bắt buộc'
            });
        }

        console.log('Sending supplement request with data:', { xaId, NgayHenTraMoi, SoBienNhan });

        const response = await axios.post(API_CONFIG.GATEWAY_URL, {
            xaId,
            NgayHenTraMoi,
            SoBienNhan
        }, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        res.json({
            success: true,
            message: 'Bổ sung hồ sơ thành công',
            data: response.data
        });

        console.log('Supplement document successful');
    } catch (error) {
        console.error('Supplement document error:', error.response?.data || error.message);
        res.status(500).json({
            success: false,
            message: 'Bổ sung hồ sơ thất bại',
            error: error.response?.data || error.message
        });
    }
});

// Serve main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start server
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});