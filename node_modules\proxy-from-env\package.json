{"_from": "proxy-from-env@^1.1.0", "_id": "proxy-from-env@1.1.0", "_inBundle": false, "_integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==", "_location": "/proxy-from-env", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "proxy-from-env@^1.1.0", "name": "proxy-from-env", "escapedName": "proxy-from-env", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/axios"], "_resolved": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "_shasum": "e102f16ca355424865755d2c9e8ea4f24d58c3e2", "_spec": "proxy-from-env@^1.1.0", "_where": "D:\\tool iLis\\node_modules\\axios", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://robwu.nl/"}, "bugs": {"url": "https://github.com/Rob--W/proxy-from-env/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Offers getProxyForUrl to get the proxy URL for a URL, respecting the *_PROXY (e.g. HTTP_PROXY) and NO_PROXY environment variables.", "devDependencies": {"coveralls": "^3.0.9", "eslint": "^6.8.0", "istanbul": "^0.4.5", "mocha": "^7.1.0"}, "homepage": "https://github.com/Rob--W/proxy-from-env#readme", "keywords": ["proxy", "http_proxy", "https_proxy", "no_proxy", "environment"], "license": "MIT", "main": "index.js", "name": "proxy-from-env", "repository": {"type": "git", "url": "git+https://github.com/Rob--W/proxy-from-env.git"}, "scripts": {"lint": "eslint *.js", "test": "mocha ./test.js --reporter spec", "test-coverage": "istanbul cover ./node_modules/.bin/_mocha -- --reporter spec"}, "version": "1.1.0"}