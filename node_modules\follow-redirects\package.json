{"_from": "follow-redirects@^1.15.0", "_id": "follow-redirects@1.15.11", "_inBundle": false, "_integrity": "sha512-deG2P0JfjrTxl50XGCDyfI97ZGVCxIpfKYmfyrQ54n5FO/0gfIES8C/Psl6kWVDolizcaaxZJnTS0QSMxvnsBQ==", "_location": "/follow-redirects", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "follow-redirects@^1.15.0", "name": "follow-redirects", "escapedName": "follow-redirects", "rawSpec": "^1.15.0", "saveSpec": null, "fetchSpec": "^1.15.0"}, "_requiredBy": ["/axios"], "_resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.11.tgz", "_shasum": "777d73d72a92f8ec4d2e410eb47352a56b8e8340", "_spec": "follow-redirects@^1.15.0", "_where": "D:\\tool iLis\\node_modules\\axios", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://ruben.verborgh.org/"}, "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.syskall.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "HTTP and HTTPS modules that follow redirects.", "devDependencies": {"concat-stream": "^2.0.0", "eslint": "^5.16.0", "express": "^4.16.4", "lolex": "^3.1.0", "mocha": "^6.0.2", "nyc": "^14.1.1"}, "engines": {"node": ">=4.0"}, "files": ["*.js"], "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "license": "MIT", "main": "index.js", "name": "follow-redirects", "peerDependenciesMeta": {"debug": {"optional": true}}, "repository": {"type": "git", "url": "git+ssh://**************/follow-redirects/follow-redirects.git"}, "scripts": {"lint": "eslint *.js test", "test": "nyc mocha"}, "version": "1.15.11"}