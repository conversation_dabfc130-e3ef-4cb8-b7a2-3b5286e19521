{"_from": "safer-buffer@>= 2.1.2 < 3", "_id": "safer-buffer@2.1.2", "_inBundle": false, "_integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "_location": "/safer-buffer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "safer-buffer@>= 2.1.2 < 3", "name": "safer-buffer", "escapedName": "safer-buffer", "rawSpec": ">= 2.1.2 < 3", "saveSpec": null, "fetchSpec": ">= 2.1.2 < 3"}, "_requiredBy": ["/iconv-lite"], "_resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "_shasum": "44fa161b0187b9549dd84bb91802f9bd8385cd6a", "_spec": "safer-buffer@>= 2.1.2 < 3", "_where": "D:\\tool iLis\\node_modules\\iconv-lite", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ChALkeR"}, "bugs": {"url": "https://github.com/ChALkeR/safer-buffer/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Modern Buffer API polyfill without footguns", "devDependencies": {"standard": "^11.0.1", "tape": "^4.9.0"}, "files": ["Porting-Buffer.md", "Readme.md", "tests.js", "dangerous.js", "safer.js"], "homepage": "https://github.com/ChALkeR/safer-buffer#readme", "license": "MIT", "main": "safer.js", "name": "safer-buffer", "repository": {"type": "git", "url": "git+https://github.com/ChALkeR/safer-buffer.git"}, "scripts": {"browserify-test": "browserify --external tape tests.js > browserify-tests.js && tape browserify-tests.js", "test": "standard && tape tests.js"}, "version": "2.1.2"}