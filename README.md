# Hệ thống hỗ trợ khách hàng iLIS

Ứng dụng web hỗ trợ khách hàng sử dụng các API của hệ thống iLIS để thực hiện các thao tác bổ sung hồ sơ.

## Tính năng

- **Đăng nhập tự động**: Kết nối với hệ thống SSO của iLIS
- **<PERSON><PERSON> sung hồ sơ**: G<PERSON>i yêu cầu bổ sung hồ sơ với thông tin cần thiết
- **Giao diện thân thiện**: Thiết kế responsive với Bootstrap 5
- **Xử lý lỗi**: Hiển thị thông báo lỗi chi tiết và hướng dẫn khắc phục

## Cài đặt

1. Clone hoặc tải về project
2. Cài đặt dependencies:
```bash
npm install
```

3. Chạy ứng dụng:
```bash
npm start
```

4. Mở trình duyệt và truy cập: `http://localhost:3000`

## Cấu trúc project

```
tool-ilis/
├── main.js              # Server backend (Express.js)
├── package.json         # Cấu hình npm
├── public/
│   ├── index.html      # Giao diện chính
│   └── script.js       # Logic frontend
└── README.md           # Tài liệu hướng dẫn
```

## API Endpoints

### 1. Đăng nhập
- **URL**: `POST /api/login`
- **Mô tả**: Lấy access token từ hệ thống SSO
- **Response**: 
```json
{
  "success": true,
  "message": "Đăng nhập thành công",
  "token": "access_token_here",
  "expires_in": 3600
}
```

### 2. Bổ sung hồ sơ
- **URL**: `POST /api/supplement-document`
- **Headers**: `Authorization: Bearer {access_token}`
- **Body**:
```json
{
  "xaId": "32092",
  "NgayHenTraMoi": "2024-07-25T10:00:00.000Z",
  "SoBienNhan": "000.05.09.H12-250724-0054"
}
```

## Hướng dẫn sử dụng

1. **Đăng nhập hệ thống**:
   - Nhấn nút "Đăng nhập" 
   - Hệ thống sẽ tự động kết nối với SSO iLIS
   - Trạng thái kết nối sẽ hiển thị màu xanh khi thành công

2. **Bổ sung hồ sơ**:
   - Điền thông tin vào form:
     - **Mã xã/phường**: Mã định danh của xã/phường (mặc định: 32092)
     - **Ngày hẹn trả mới**: Chọn ngày và giờ hẹn trả
     - **Số biên nhận**: Nhập số biên nhận theo định dạng
   - Nhấn "Bổ sung hồ sơ" để gửi yêu cầu
   - Kết quả sẽ hiển thị bên dưới form

## Cấu hình API

Các thông tin API được cấu hình trong file `main.js`:

```javascript
const API_CONFIG = {
    SSO_URL: 'https://ilis-sso.vnpt.vn/connect/token',
    GATEWAY_URL: 'https://ilis-gateway.vnpt.vn/lpm/api/DVCIGateV2/BoSungHoSo',
    CLIENT_CREDENTIALS: {
        username: 'admin.camau2',
        password: 'D2D@Kw2Kh2',
        grant_type: 'password',
        client_id: 'vilis-mobile-client',
        client_secret: 'n)3b^Q7g]Jd6T&$^'
    }
};
```

## Lưu ý bảo mật

- Thông tin đăng nhập được lưu trữ trong code (chỉ dùng cho môi trường phát triển)
- Trong môi trường production, nên sử dụng biến môi trường để lưu trữ thông tin nhạy cảm
- Access token được lưu trong bộ nhớ server (không persistent)

## Xử lý lỗi

Ứng dụng xử lý các loại lỗi phổ biến:
- Lỗi kết nối mạng
- Lỗi xác thực (token hết hạn)
- Lỗi validation dữ liệu đầu vào
- Lỗi từ API backend

## Phát triển thêm

Có thể mở rộng ứng dụng với các tính năng:
- Lưu trữ lịch sử giao dịch
- Xuất báo cáo
- Quản lý nhiều tài khoản
- Tích hợp với các API khác của iLIS

## Hỗ trợ

Nếu gặp vấn đề, vui lòng kiểm tra:
1. Kết nối internet
2. Thông tin đăng nhập API
3. Log trong console của trình duyệt
4. Log trong terminal chạy server
