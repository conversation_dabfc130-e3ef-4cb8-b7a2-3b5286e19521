{"_from": "debug@2.6.9", "_id": "debug@2.6.9", "_inBundle": false, "_integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "_location": "/debug", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "debug@2.6.9", "name": "debug", "escapedName": "debug", "rawSpec": "2.6.9", "saveSpec": null, "fetchSpec": "2.6.9"}, "_requiredBy": ["/body-parser", "/express", "/finalhandler", "/send"], "_resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "_shasum": "5d128515df134ff327e90a4c93f4e077a536341f", "_spec": "debug@2.6.9", "_where": "D:\\tool iLis\\node_modules\\express", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": "./src/browser.js", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "bundleDependencies": false, "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"ms": "2.0.0"}, "deprecated": false, "description": "small debugging utility", "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "homepage": "https://github.com/visionmedia/debug#readme", "keywords": ["debug", "log", "debugger"], "license": "MIT", "main": "./src/index.js", "name": "debug", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "version": "2.6.9"}