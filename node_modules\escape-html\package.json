{"name": "escape-html", "description": "Escape string for use in HTML", "version": "1.0.3", "license": "MIT", "keywords": ["escape", "html", "utility"], "repository": "component/escape-html", "devDependencies": {"benchmark": "1.0.0", "beautify-benchmark": "0.2.4"}, "files": ["LICENSE", "Readme.md", "index.js"], "scripts": {"bench": "node benchmark/index.js"}, "_resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "_integrity": "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==", "_from": "escape-html@^1.0.3"}