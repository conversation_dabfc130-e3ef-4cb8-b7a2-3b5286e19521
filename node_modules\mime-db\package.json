{"_from": "mime-db@1.52.0", "_id": "mime-db@1.52.0", "_inBundle": false, "_integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "_location": "/mime-db", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "mime-db@1.52.0", "name": "mime-db", "escapedName": "mime-db", "rawSpec": "1.52.0", "saveSpec": null, "fetchSpec": "1.52.0"}, "_requiredBy": ["/mime-types"], "_resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "_shasum": "bbabcdc02859f4987301c856e3387ce5ec43bf70", "_spec": "mime-db@1.52.0", "_where": "D:\\tool iLis\\node_modules\\mime-types", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}], "deprecated": false, "description": "Media Type Database", "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "4.16.3", "eslint": "7.32.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.1.1", "eslint-plugin-standard": "4.1.0", "gnode": "0.1.2", "media-typer": "1.1.0", "mocha": "9.2.1", "nyc": "15.1.0", "raw-body": "2.5.0", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "homepage": "https://github.com/jshttp/mime-db#readme", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "name": "mime-db", "repository": {"type": "git", "url": "git+https://github.com/jshttp/mime-db.git"}, "scripts": {"build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "1.52.0"}