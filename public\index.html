<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> thống hỗ trợ khách hàng - iLIS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        .btn-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
        select.form-control {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px 12px;
            padding-right: 40px;
            appearance: none;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .loading {
            display: none;
        }
        .spinner-border {
            width: 1rem;
            height: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h1 class="text-white mb-3">
                <i class="fas fa-headset me-2"></i>
                Hệ thống hỗ trợ khách hàng iLIS
            </h1>
            <p class="text-white-50">Công cụ bổ sung hồ sơ và quản lý dịch vụ</p>
        </div>

        <!-- Login Section -->
        <div class="card" id="loginCard">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Đăng nhập hệ thống
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <span class="status-indicator" id="connectionStatus"></span>
                    <span id="connectionText">Chưa kết nối</span>
                </div>
                <button class="btn btn-primary" id="loginBtn" onclick="login()">
                    <span class="loading spinner-border spinner-border-sm me-2" id="loginSpinner"></span>
                    <i class="fas fa-key me-2"></i>
                    Đăng nhập
                </button>
                <div id="loginResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Document Supplement Section -->
        <div class="card" id="supplementCard" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-plus me-2"></i>
                    Bổ sung hồ sơ
                </h5>
            </div>
            <div class="card-body">
                <form id="supplementForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="xaId" class="form-label">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                Chọn xã/phường
                            </label>
                            <select class="form-control" id="xaId" required>
                                <option value="">-- Chọn xã/phường --</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="NgayHenTraMoi" class="form-label">
                                <i class="fas fa-calendar-alt me-1"></i>
                                Ngày hẹn trả mới
                            </label>
                            <input type="datetime-local" class="form-control" id="NgayHenTraMoi" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="SoBienNhan" class="form-label">
                            <i class="fas fa-receipt me-1"></i>
                            Số biên nhận
                        </label>
                        <input type="text" class="form-control" id="SoBienNhan" 
                               placeholder="VD: 000.05.09.H12-250724-0054" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <span class="loading spinner-border spinner-border-sm me-2" id="supplementSpinner"></span>
                        <i class="fas fa-paper-plane me-2"></i>
                        Bổ sung hồ sơ
                    </button>
                </form>
                <div id="supplementResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Hướng dẫn sử dụng
                </h5>
            </div>
            <div class="card-body">
                <ol class="mb-0">
                    <li class="mb-2">Nhấn nút "Đăng nhập" để kết nối với hệ thống iLIS</li>
                    <li class="mb-2">Sau khi đăng nhập thành công, form bổ sung hồ sơ sẽ hiển thị</li>
                    <li class="mb-2">Điền đầy đủ thông tin: Mã xã/phường, Ngày hẹn trả mới, Số biên nhận</li>
                    <li class="mb-0">Nhấn "Bổ sung hồ sơ" để gửi yêu cầu</li>
                </ol>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
